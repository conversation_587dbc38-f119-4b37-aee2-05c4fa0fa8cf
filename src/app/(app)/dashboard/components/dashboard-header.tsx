"use client";

import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

import { useGetMyOrg } from "@/app/services/organization.hooks";
import { THEME_OPTIONS } from "@/app/services/ThemeProvider";

import { AppLogo } from "../../../components/app-logo";
import { CustomThemeSwitch } from "../../../components/custom-theme-switch";
import { DashboardUser } from "./dashboard-navbar/dashboard-user";

export const DashboardHeader = () => {
  const { data: organization, isLoading, orgStatus } = useGetMyOrg();
  const { setTheme, theme: currentTheme } = useTheme();
  const [isDark, setIsDark] = useState(false);

  const onThemeChange = (darkEnabled: boolean) => {
    setTheme(darkEnabled ? THEME_OPTIONS.dark : THEME_OPTIONS.light);
  };

  useEffect(() => {
    setIsDark(currentTheme === THEME_OPTIONS.dark);
  }, [currentTheme]);

  return (
    <header className="flex w-full items-center justify-between border-b border-white/10 bg-dashboardBG px-6 py-4">
      <div className="flex items-center">
        <AppLogo />
      </div>

      <div className="flex items-center gap-4">
        <div className="rounded-full border border-white/60 px-2 py-1">
          <CustomThemeSwitch onChange={onThemeChange} enabled={isDark} />
        </div>

        <div className="h-6 w-px bg-white/75" />

        <div className="flex items-center gap-2">
          <DashboardUser
            {...{
              organization,
              isLoading,
              orgStatus,
            }}
          />
        </div>
      </div>
    </header>
  );
};
