"use client";

import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { FaMoon, FaSun } from "react-icons/fa";

import { useGetMyOrg } from "@/app/services/organization.hooks";
import { THEME_OPTIONS } from "@/app/services/ThemeProvider";

import { AppLogo } from "../../../components/app-logo";
import { AppSwitch } from "../../../components/app-switch";
import { DashboardUser } from "./dashboard-navbar/dashboard-user";

export const DashboardHeader = () => {
  const { data: organization, isLoading, orgStatus } = useGetMyOrg();
  const { setTheme, theme: currentTheme } = useTheme();
  const [isDark, setIsDark] = useState(false);

  const onThemeChange = (darkEnabled: boolean) => {
    setTheme(darkEnabled ? THEME_OPTIONS.dark : THEME_OPTIONS.light);
  };

  const ThemeIcon = isDark ? FaSun : FaMoon;

  useEffect(() => {
    setIsDark(currentTheme === THEME_OPTIONS.dark);
  }, [currentTheme]);

  return (
    <header className="flex w-full items-center justify-between border-b border-white/10 bg-dashboardBG px-6 py-4">
      <div className="flex items-center">
        <AppLogo />
      </div>

      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2 rounded-full border border-white/60 px-2 py-1">
          <div className="flex size-[17.5px] items-center justify-center">
            <ThemeIcon className="text-white/60" size={12} />
          </div>

          <div className="relative size-7 rounded-full border border-[#A4A3A3] bg-transparent">
            <AppSwitch
              className="absolute inset-0 opacity-0"
              onChange={onThemeChange}
              enabled={isDark}
            />
            <div
              className={`absolute top-1/2 size-3 -translate-y-1/2 rounded-full bg-white transition-transform duration-200 ${
                isDark ? "translate-x-3.5" : "translate-x-0.5"
              }`}
            />
          </div>
        </div>

        <div className="h-6 w-px bg-white/75" />

        <div className="flex items-center gap-2">
          <DashboardUser
            {...{
              organization,
              isLoading,
              orgStatus,
            }}
          />
        </div>
      </div>
    </header>
  );
};
