import noop from "lodash/noop";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useTheme } from "next-themes";
import type { IconType } from "react-icons/lib";
import {
  MdOutlineKeyboardArrowDown,
  MdOutlineKeyboardArrowUp,
} from "react-icons/md";

import { pathMatch } from "@/app/helpers";
import { cn } from "@/app/helpers/cn";
import { useBooleanState } from "@/app/services/helpers";
import { THEME_OPTIONS } from "@/app/services/ThemeProvider";

import { AppSpinner } from "../../../../components/app-spinner";

export interface ABIChildItem {
  label: string;
  icon: IconType;
  href: string;
}

export interface SidebarItemProps {
  icon: IconType;
  label: string;
  childItems?: ABIChildItem[];
  isChildItemsLoading?: boolean;
  href?: string;
  newTab?: boolean;
  type?: "link" | "button";
}

export const SidebarItem = ({
  icon: Icon,
  label,
  childItems,
  isChildItemsLoading,
  href,
  newTab,
  type,
}: SidebarItemProps) => {
  const [childOpen, { toggle: onToggleChildren }] = useBooleanState(false);
  const pathname = usePathname();
  const { theme } = useTheme();

  const isActive = pathMatch(pathname, href) && (!childItems || !childOpen);
  const hasChildItems = Array.isArray(childItems) && childItems.length > 0;

  const itemClasses = cn(
    "flex items-center py-[13px] pl-[28px] gap-[8px] text-[16px] hyphens-auto w-full justify-start pr-[24px] overflow-ellipsis whitespace-nowrap",
    {
      "bg-primaryBg text-white":
        hasChildItems && childOpen && theme === THEME_OPTIONS.dark,
      "bg-primaryBg text-primary":
        hasChildItems && childOpen && theme === THEME_OPTIONS.light,
      "bg-primary text-white": isActive && !hasChildItems,
      "text-textPrimary":
        (!isActive && !hasChildItems) ||
        (!childOpen && hasChildItems && theme === THEME_OPTIONS.dark),
    }
  );

  const childItemClasses = (isActive) =>
    cn(
      "py-[13px] pl-[28px]  text-[16px] flex w-[100%] items-center gap-[8px] truncate",
      {
        "bg-primaryBg text-textPrimary":
          !isActive && theme === THEME_OPTIONS.dark,
        "bg-primaryBg text-primary  ":
          !isActive && theme === THEME_OPTIONS.light,
        "bg-primary text-white": isActive,
      }
    );

  const wrapInLink = (children) =>
    href ? (
      <Link href={href} target={newTab ? "_blank" : undefined}>
        {children}
      </Link>
    ) : (
      children
    );

  const ChevronIcon = childOpen
    ? MdOutlineKeyboardArrowDown
    : MdOutlineKeyboardArrowUp;

  const item = (
    <button
      className={itemClasses}
      onClick={hasChildItems ? onToggleChildren : noop}
      aria-label={label}
    >
      <Icon className="w-[20px]" />
      {label}
      {hasChildItems && <ChevronIcon className="ml-auto size-[15px]" />}
    </button>
  );

  return (
    <li>
      {type === "link" ? wrapInLink(item) : item}
      {hasChildItems &&
        childOpen &&
        (isChildItemsLoading ? (
          <div className="flex h-[58px] items-center justify-center">
            <AppSpinner />
          </div>
        ) : (
          <div className="bg-primaryBg">
            {childItems.map(({ href, label, icon: Icon }) => (
              <div
                className={childItemClasses(pathMatch(pathname, href))}
                key={label}
              >
                <Icon className="w-[20px]" />
                <Link
                  href={href}
                  title={label}
                  key={href}
                  className="w-[230px] truncate"
                >
                  {label}
                </Link>
              </div>
            ))}
          </div>
        ))}
    </li>
  );
};
