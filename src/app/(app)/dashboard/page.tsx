"use client";

import Link from "next/link";

import { App<PERSON><PERSON><PERSON> } from "../../components/app-button";
import { H1, Body4 } from "../../components/app-typography";
import { OrgStatus, useGetMyOrg } from "../../services/organization.hooks";
import DashboardPlan from "./components/dashboard-plan/dashboard-plan";
import DashboardSavedProjects from "./components/dashboard-saved-projects/dashboard-saved-projects";
import {
  AppVideoCarousel,
  DASHBOARD_VIDEOS,
} from "@/app/components/AppVideosCarousel/AppVideosCarousel";

export default function Dashboard() {
  const { isLoading, orgStatus } = useGetMyOrg();

  return (
    <div className="min-h-screen bg-dashboardBG">
      <div className="mx-auto max-w-7xl px-6 py-12">
        <div className="mb-16 flex flex-col gap-1">
          <H1 className="text-primary">Welcome to Recon V1.0!</H1>
          <Body4 className="text-fore-neutral-secondary">
            Recon helps you write and run invariant tests with Echidna and
            Medusa
          </Body4>
          <Body4 className="text-fore-neutral-secondary">
            Check the videos below or click around to learn more!
          </Body4>
          <Body4 className="text-fore-neutral-secondary">
            Recon Builder is Free to use for Open Source Foundry Projects
          </Body4>
          <Body4 className="text-fore-neutral-secondary">
            If you want to Run Invariant Tests in the Cloud, talk to us about
            Recon Pro!
          </Body4>
        </div>

        <div className="mb-8">
          <Link href="https://book.getrecon.xyz/" target="_blank">
            <AppButton>Written Tutorials</AppButton>
          </Link>
        </div>

        {orgStatus === OrgStatus.NOORG && !isLoading && (
          <div className="mb-16">
            <Link href="/dashboard/onboard">
              <H1 className="mb-4 text-primary">You don't have an account</H1>
              <AppButton>Create an account!</AppButton>
            </Link>
          </div>
        )}

        <div className="mb-16">
          <AppVideoCarousel videos={DASHBOARD_VIDEOS} />
        </div>

        <div className="mb-16">
          <DashboardPlan />
        </div>

        <div className="mb-16">
          <DashboardSavedProjects />
        </div>
      </div>
    </div>
  );
}
